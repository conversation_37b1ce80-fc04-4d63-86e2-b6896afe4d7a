<?php

namespace Domain\Payment\Stripe\Webhook;

use App\Jobs\ProcessMarketplacePayment;
use App\Models\MarketplaceCheckoutSession;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Webhook;

trait processOrder
{
    public function processOrder(Request $request): JsonResponse|null
    {

        $event = $this->verifyWebhook($request);

        // -----------------------
        // Process Event Type
        switch ($event->type) {
            case 'charge.succeeded':
                $paymentIntent = $event->data->object;
                $stripePaymentIntent = $paymentIntent->payment_intent;
                $checkoutSession = MarketplaceCheckoutSession::where('stripe_payment_intent', $stripePaymentIntent)->first();
                ProcessMarketplacePayment::dispatch($stripePaymentIntent, $checkoutSession->user_id)->onQueue('orders');
                Log::info('Payment succeeded: ' . json_encode($paymentIntent));
                break;

            case 'payment_intent.payment_failed':
                $paymentIntent = $event->data->object;
                Log::info('Payment failed: ' . json_encode($paymentIntent));
                break;

            default:
                //Log::info('Received unknown event type From Stripe: ' . $event->type);
        }

        return response()->json(['status' => 'success']);
    }

    public function verifyWebhook(Request $request)
    {
        // -----------------------
        // Initialize Stripe Client
        $webhookSecret = config('services.stripe.webhook');

        // -----------------------
        // Get Request Data
        $payload = $request->getContent();
        $signatureHeader = $request->header('Stripe-Signature');

        // -----------------------
        // Validate Webhook Signature
        try {
            $event = Webhook::constructEvent(
                $payload,
                $signatureHeader,
                $webhookSecret,
            );
        } catch (\UnexpectedValueException $e) {
            throw new \Exception('Invalid payload: ' . $e->getMessage());
        } catch (SignatureVerificationException $e) {
            throw new \Exception('Invalid signature: ' . $e->getMessage());
        }

        return $event;
    }
}
