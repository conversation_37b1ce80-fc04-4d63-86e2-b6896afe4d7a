<?php

namespace App\Services\Publisher;

use App\Http\Controllers\Publisher\Resources\OrderItemsResources as PublisherOrderItemsResources;
use App\Http\Controllers\Publisher\Resources\OrderResource as PublisherOrderResource;
use App\Http\Resources\MediaResource;
use App\Models\MarketplaceOrder;
use App\Models\MarketplaceSingleOrderItem;

class PublisherOrderService
{
    /*********************************************************************
     * GET PENDING ORDER ITEMS
     *********************************************************************
     *
     * Get all pending order items excluding completed, refunded, and cancelled items.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     *********************************************************************/
    public function getPendingOrderItems()
    {

        $pendingOrderItems = MarketplaceSingleOrderItem::without(['content', 'requirements'])
            ->with(['order', 'order.user', 'website'])
            ->whereNotIn('state', [
                \App\Enums\OrderItemStates::OrderItemCompleted->value,
                \App\Enums\OrderItemStates::PublicationDelivered->value,
                \App\Enums\OrderItemStates::RefundedToWallet->value,
                \App\Enums\OrderItemStates::OrderItemCancelled->value,
            ])
            ->orderBy('created_at', 'desc')
            ->paginate(10);


        return PublisherOrderItemsResources::collection($pendingOrderItems);
    }


    /*********************************************************************
     * GET COMPLETED ORDERS
     *********************************************************************
     *
     * Get all completed orders.
     *
     *********************************************************************/
    public function getCompletedOrders()
    {
        $completedOrders = MarketplaceOrder::orderCompleted()
            ->with(['user', 'orderItems'])
            ->orderBy('created_at', 'desc')
            ->get();

        return PublisherOrderResource::collection($completedOrders)->resolve();
    }


    /*********************************************************************
     * GET COMPLETED ORDER ITEMS
     *********************************************************************
     *
     * Get all completed order items including delivered, refunded, and cancelled items.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     *********************************************************************/
    public function getCompletedOrderItems()
    {
        $completedOrderItems = MarketplaceSingleOrderItem::without(['content', 'requirements'])
            ->with(['order', 'order.user', 'website'])
            ->whereIn('state', [
                \App\Enums\OrderItemStates::OrderItemCompleted->value,
                \App\Enums\OrderItemStates::PublicationDelivered->value,
                \App\Enums\OrderItemStates::RefundedToWallet->value,
                \App\Enums\OrderItemStates::OrderItemCancelled->value,
            ])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return PublisherOrderItemsResources::collection($completedOrderItems);
    }


    /*********************************************************************
     * GET ORDER DETAILS
     *********************************************************************
     *
     * Get the details of a specific order.
     *
     *********************************************************************/
    public function getOrderDetails(MarketplaceSingleOrderItem $item)
    {

        // ------------------------------
        // Get Order Item with Relations
        $item->load([
            'order.user.advertiserGuidelines',
            'messages',
            'publication',
            'content.media',
            'content.writer',
        ]);

        // Get media files - try multiple approaches
        $media = collect();

        // First, try the proper polymorphic relationship
        if ($item->content && $item->content->media) {
            $media = $media->merge($item->content->media);
        }

        // If no media found, try to find media by files_array
        if ($media->isEmpty() && $item->content && !empty($item->content->files_array)) {
            $mediaByIds = \App\Models\Media::whereIn('id', $item->content->files_array)->get();
            $media = $media->merge($mediaByIds);
        }

        return [
            'order' => $item->order,
            'item' => $item,
            'media' => MediaResource::collection($media)->resolve(),
        ];
    }
}
